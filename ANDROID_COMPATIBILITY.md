# FarmHelper Android/PojavLauncher Compatibility

Este documento descreve as modificações feitas no FarmHelper para torná-lo compatível com Android através do PojavLauncher.

## Problema Original

O mod FarmHelper utilizava as classes `SystemTray` e `TrayIcon` do Java AWT para mostrar notificações do sistema. Estas classes não estão disponíveis no ambiente Android do PojavLauncher, causando crashes e falhas no sistema de failsafes.

## Solução Implementada

### 1. Detecção de Ambiente Android

Foi implementado um sistema de detecção automática para identificar se o mod está rodando em um ambiente Android/PojavLauncher:

```java
private static boolean isAndroidEnvironment() {
    String osName = System.getProperty("os.name", "").toLowerCase();
    String javaVendor = System.getProperty("java.vendor", "").toLowerCase();
    String javaVmName = System.getProperty("java.vm.name", "").toLowerCase();
    
    return osName.contains("linux") && 
        (javaVendor.contains("android") || 
         javaVmName.contains("art") || 
         System.getProperty("java.home", "").contains("android") ||
         System.getProperty("user.dir", "").contains("pojav"));
}
```

### 2. Sistema de Notificações Adaptativo

O sistema de notificações foi completamente reescrito para funcionar em ambos os ambientes:

#### Para Android/PojavLauncher:
- Logs detalhados no console
- Mensagens no chat do jogo
- Notificações via webhook (se configurado)

#### Para Desktop:
- Tentativa de usar SystemTray via reflexão
- Fallback para logs se SystemTray não estiver disponível
- Mantém compatibilidade total com o comportamento original

### 3. Arquivos Modificados

#### `farmhelper-mod/src/main/java/com/jelly/farmhelper/features/Failsafe.java`

**Mudanças principais:**
- Removidos imports diretos de `java.awt.*`
- Adicionado método `isAndroidEnvironment()` para detecção de plataforma
- Reescrito método `createNotification()` com suporte adaptativo
- Adicionado método `createFallbackNotification()` para casos de erro
- Uso de reflexão para acessar classes AWT quando disponíveis

**Funcionalidades mantidas:**
- Todos os tipos de failsafe continuam funcionando
- Sistema de logs inalterado
- Configurações de notificação respeitadas
- Compatibilidade total com desktop

#### `farmhelper-mod/src/main/java/com/jelly/farmhelper/FarmHelper.java`

**Mudanças principais:**
- Reescrito método `registerInitNotification()` com detecção de ambiente
- Uso de reflexão para SystemTray quando disponível
- Fallback para notificações de console

### 4. Benefícios da Solução

1. **Compatibilidade Total**: O mod funciona tanto no desktop quanto no Android
2. **Detecção Automática**: Não requer configuração manual do usuário
3. **Graceful Degradation**: Se SystemTray falhar, usa métodos alternativos
4. **Logs Melhorados**: Notificações mais visíveis no ambiente Android
5. **Zero Breaking Changes**: Mantém toda funcionalidade existente

### 5. Comportamento das Notificações

#### No Desktop (comportamento original):
- Notificações do sistema via SystemTray
- Logs no console
- Mensagens no chat do jogo

#### No Android/PojavLauncher:
- Logs destacados no console com formatação especial
- Mensagens coloridas no chat do jogo
- Notificações via webhook (se configurado)
- Sem tentativas de usar SystemTray

### 6. Exemplo de Saída no Android

```
=== FAILSAFE NOTIFICATION ===
Farm Helper - Failsafes: You have been bedrock checked
=============================
```

No chat do jogo:
```
[FAILSAFE] You have been bedrock checked
```

### 7. Configuração

Nenhuma configuração adicional é necessária. O sistema detecta automaticamente o ambiente e adapta o comportamento das notificações.

### 8. Testes Recomendados

Para verificar se a implementação está funcionando corretamente:

1. **No Desktop**: Verificar se as notificações do sistema ainda aparecem
2. **No Android**: Verificar se os logs aparecem no console e mensagens no chat
3. **Failsafes**: Testar todos os tipos de failsafe em ambos os ambientes
4. **Configurações**: Verificar se a opção de desabilitar notificações funciona

### 9. Compatibilidade Futura

Esta implementação é robusta e deve continuar funcionando mesmo com atualizações do PojavLauncher ou mudanças no ambiente Android, pois:

- Usa detecção baseada em propriedades do sistema
- Implementa fallbacks múltiplos
- Não depende de APIs específicas do Android
- Mantém compatibilidade com Java 8

### 10. Conclusão

As modificações implementadas resolvem completamente o problema de compatibilidade com Android/PojavLauncher, mantendo toda a funcionalidade original do mod e adicionando robustez através de detecção automática de ambiente e sistemas de fallback.
