{"required": true, "compatibilityLevel": "JAVA_8", "package": "com.jelly.farmhelper.mixins", "refmap": "mixins.farmhelper.refmap.json", "mixins": ["block.IBlockAccessor", "block.MixinBlockCocoa", "block.MixinBlockCrops", "block.MixinBlockMushroom", "block.MixinBlockNetherWart", "client.MinecraftMixin", "client.MixinChunk", "client.MixinInventoryPlayer", "client.MixinSoundManager", "fml.MixinFMLHandshakeMessage", "gui.MixinGuiDisconnected", "network.MixinNetworkManager", "network.MixinNetworkManagerChInit"], "client": ["block.MixinBlock<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "client.MixinPlayerControllerMP", "gui<PERSON>", "gui.MixinGuiInventory", "gui.MixinGuiMainMenu", "gui.MixinGuiMultiplayer", "gui.MixinGuiScreen", "network.MixinNetHandlerPlayClient", "render.MixinEffectRenderer", "render.MixinInventoryEffectRenderer"]}